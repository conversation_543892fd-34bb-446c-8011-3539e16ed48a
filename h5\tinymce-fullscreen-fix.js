/**
 * TinyMCE 全屏模式工具栏修复脚本
 * 解决全屏模式下工具栏和菜单栏消失的问题
 */
(function() {
    'use strict';
    
    console.log('TinyMCE 全屏修复脚本已加载');
    
    // 强制添加全屏修复样式
    function addFullscreenFixStyles() {
        const style = document.createElement('style');
        style.id = 'tinymce-fullscreen-fix-styles';
        style.textContent = `
            /* 修复全屏模式下的布局问题 - 只针对 TinyMCE 编辑器 */
            .tox-fullscreen.tox-tinymce {
                z-index: 10000 !important;
                display: flex !important;
                flex-direction: column !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
            }

            /* 编辑器容器使用 flex 布局 */
            .tox-fullscreen.tox-tinymce .tox-editor-container {
                display: flex !important;
                flex-direction: column !important;
                height: 100% !important;
                width: 100% !important;
            }

            /* 确保工具栏和菜单栏显示，保持原有顺序 */
            .tox-fullscreen.tox-tinymce .tox-editor-header {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 10001 !important;
                flex-shrink: 0 !important;
                background: #fff !important;
                border-bottom: 1px solid #ccc !important;
            }

            .tox-fullscreen.tox-tinymce .tox-menubar {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 10001 !important;
                height: auto !important;
                min-height: 30px !important;
                flex-shrink: 0 !important;
                background: #f0f0f0 !important;
            }

            .tox-fullscreen.tox-tinymce .tox-toolbar,
            .tox-fullscreen.tox-tinymce .tox-toolbar-overlord {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 10001 !important;
                height: auto !important;
                min-height: 40px !important;
                flex-shrink: 0 !important;
                background: #f8f8f8 !important;
                border-bottom: 1px solid #ddd !important;
            }

            /* 编辑区域占据剩余空间，不改变顺序 */
            .tox-fullscreen.tox-tinymce .tox-edit-area {
                flex: 1 !important;
                overflow: auto !important;
                padding: 0 !important;
                margin: 0 !important;
                height: auto !important;
                min-height: 0 !important;
            }

            /* 编辑器内容区域 */
            .tox-fullscreen.tox-tinymce .tox-edit-area__iframe {
                width: 100% !important;
                height: 100% !important;
                border: none !important;
            }

            /* 状态栏保持在底部，不改变顺序 */
            .tox-fullscreen.tox-tinymce .tox-statusbar {
                flex-shrink: 0 !important;
                position: relative !important;
                z-index: 10001 !important;
            }

            /* 确保所有子元素正确布局 */
            .tox-fullscreen.tox-tinymce * {
                box-sizing: border-box !important;
            }
        `;
        
        // 移除旧样式（如果存在）
        const oldStyle = document.getElementById('tinymce-fullscreen-fix-styles');
        if (oldStyle) {
            oldStyle.remove();
        }
        
        document.head.appendChild(style);
        console.log('全屏修复样式已添加');
    }
    
    // 等待 TinyMCE 加载完成
    function waitForTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            setupFullscreenFix();
        } else {
            setTimeout(waitForTinyMCE, 100);
        }
    }
    
    function setupFullscreenFix() {
        console.log('设置 TinyMCE 全屏修复');
        
        // 立即添加修复样式
        addFullscreenFixStyles();
        
        // 监听 TinyMCE 编辑器初始化
        tinymce.on('AddEditor', function(e) {
            var editor = e.editor;
            console.log('编辑器已添加:', editor.id);
            
            // 监听全屏状态变化
            editor.on('FullscreenStateChanged', function(e) {
                console.log('全屏状态变化:', e.state);
                
                if (e.state) {
                    // 进入全屏模式
                    console.log('进入全屏模式，开始修复');
                    
                    // 立即修复
                    fixFullscreenToolbar();
                    
                    // 多次尝试修复，确保成功
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 50);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 200);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 500);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 1000);
                } else {
                    // 退出全屏模式
                    console.log('退出全屏模式');
                }
            });
        });
    }
    
    function fixFullscreenToolbar() {
        console.log('开始修复全屏工具栏布局');

        // 查找全屏容器
        var fullscreenContainer = document.querySelector('.tox-fullscreen');
        if (!fullscreenContainer) {
            console.log('未找到全屏容器，尝试查找其他可能的容器');
            // 尝试查找其他可能的全屏容器
            fullscreenContainer = document.querySelector('.tox-editor-container[style*="position: fixed"]') ||
                                 document.querySelector('.tox-editor-container[style*="z-index"]') ||
                                 document.querySelector('.tox-tinymce[style*="position: fixed"]');
        }

        if (!fullscreenContainer) {
            console.log('仍未找到全屏容器');
            return;
        }

        console.log('找到全屏容器:', fullscreenContainer);

        // 设置全屏容器为 flex 布局
        fullscreenContainer.style.position = 'fixed';
        fullscreenContainer.style.top = '0';
        fullscreenContainer.style.left = '0';
        fullscreenContainer.style.width = '100%';
        fullscreenContainer.style.height = '100%';
        fullscreenContainer.style.zIndex = '10000';
        fullscreenContainer.style.display = 'flex';
        fullscreenContainer.style.flexDirection = 'column';

        // 查找编辑器容器并设置 flex 布局
        var editorContainer = fullscreenContainer.querySelector('.tox-editor-container');
        if (editorContainer) {
            editorContainer.style.display = 'flex';
            editorContainer.style.flexDirection = 'column';
            editorContainer.style.height = '100%';
            editorContainer.style.width = '100%';
            console.log('编辑器容器布局已设置');
        }

        // 修复编辑器头部（包含菜单栏和工具栏）- 保持原有顺序
        var editorHeader = fullscreenContainer.querySelector('.tox-editor-header');
        if (editorHeader) {
            editorHeader.style.display = 'block';
            editorHeader.style.visibility = 'visible';
            editorHeader.style.position = 'relative';
            editorHeader.style.top = '0';
            editorHeader.style.zIndex = '10001';
            editorHeader.style.flexShrink = '0';
            editorHeader.style.background = '#fff';
            console.log('编辑器头部已修复');
        }

        // 修复菜单栏 - 保持原有顺序
        var menubar = fullscreenContainer.querySelector('.tox-menubar');
        if (menubar) {
            menubar.style.display = 'flex';
            menubar.style.visibility = 'visible';
            menubar.style.opacity = '1';
            menubar.style.position = 'relative';
            menubar.style.zIndex = '10001';
            menubar.style.height = 'auto';
            menubar.style.minHeight = '30px';
            menubar.style.flexShrink = '0';
            menubar.style.background = '#f0f0f0';
            console.log('菜单栏已修复');
        }

        // 修复工具栏 - 保持原有顺序
        var toolbar = fullscreenContainer.querySelector('.tox-toolbar');
        if (toolbar) {
            toolbar.style.display = 'flex';
            toolbar.style.visibility = 'visible';
            toolbar.style.opacity = '1';
            toolbar.style.position = 'relative';
            toolbar.style.zIndex = '10001';
            toolbar.style.height = 'auto';
            toolbar.style.minHeight = '40px';
            toolbar.style.flexShrink = '0';
            toolbar.style.background = '#f8f8f8';
            console.log('工具栏已修复');
        }

        // 修复工具栏容器 - 保持原有顺序
        var toolbarOverlord = fullscreenContainer.querySelector('.tox-toolbar-overlord');
        if (toolbarOverlord) {
            toolbarOverlord.style.display = 'flex';
            toolbarOverlord.style.visibility = 'visible';
            toolbarOverlord.style.height = 'auto';
            toolbarOverlord.style.flexShrink = '0';
            console.log('工具栏容器已修复');
        }

        // 修复编辑区域 - 让它占据剩余空间，但不改变顺序
        var editArea = fullscreenContainer.querySelector('.tox-edit-area');
        if (editArea) {
            editArea.style.flex = '1';
            editArea.style.overflow = 'auto';
            editArea.style.padding = '0';
            editArea.style.margin = '0';
            editArea.style.height = 'auto';
            editArea.style.minHeight = '0';
            console.log('编辑区域已修复');
        }

        // 修复编辑器 iframe
        var iframe = fullscreenContainer.querySelector('.tox-edit-area__iframe');
        if (iframe) {
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';
            console.log('编辑器 iframe 已修复');
        }

        // 修复状态栏 - 保持原有顺序
        var statusbar = fullscreenContainer.querySelector('.tox-statusbar');
        if (statusbar) {
            statusbar.style.flexShrink = '0';
            statusbar.style.position = 'relative';
            statusbar.style.zIndex = '10001';
            console.log('状态栏已修复');
        }

        console.log('全屏工具栏布局修复完成');
    }
    
    // 页面加载完成后开始监听
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            addFullscreenFixStyles();
            waitForTinyMCE();
        });
    } else {
        addFullscreenFixStyles();
        waitForTinyMCE();
    }
    
    // 额外的全局修复函数
    window.fixTinyMCEFullscreen = function() {
        console.log('手动触发全屏修复');
        addFullscreenFixStyles();
        fixFullscreenToolbar();
    };
    
})();
