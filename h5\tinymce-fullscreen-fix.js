/**
 * TinyMCE 全屏模式工具栏修复脚本
 * 解决全屏模式下工具栏和菜单栏消失的问题
 */
(function() {
    'use strict';
    
    console.log('TinyMCE 全屏修复脚本已加载');
    
    // 强制添加全屏修复样式
    function addFullscreenFixStyles() {
        const style = document.createElement('style');
        style.id = 'tinymce-fullscreen-fix-styles';
        style.textContent = `
            /* 强制显示全屏模式下的工具栏和菜单栏 */
            .tox-fullscreen {
                z-index: 10000 !important;
            }
            
            .tox-fullscreen .tox-toolbar,
            .tox-fullscreen .tox-menubar,
            .tox-fullscreen .tox-toolbar-overlord,
            .tox-fullscreen .tox-editor-header {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 10001 !important;
                height: auto !important;
                min-height: 40px !important;
            }
            
            .tox-fullscreen .tox-toolbar-overlord {
                display: block !important;
            }
            
            .tox-fullscreen .tox-editor-header {
                display: block !important;
                position: relative !important;
                top: 0 !important;
            }
            
            /* 确保全屏模式下的编辑器容器正确显示 */
            .tox-fullscreen .tox-edit-area {
                padding-top: 0 !important;
            }
            
            /* 修复可能的隐藏问题 */
            .tox-fullscreen * {
                box-sizing: border-box !important;
            }
        `;
        
        // 移除旧样式（如果存在）
        const oldStyle = document.getElementById('tinymce-fullscreen-fix-styles');
        if (oldStyle) {
            oldStyle.remove();
        }
        
        document.head.appendChild(style);
        console.log('全屏修复样式已添加');
    }
    
    // 等待 TinyMCE 加载完成
    function waitForTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            setupFullscreenFix();
        } else {
            setTimeout(waitForTinyMCE, 100);
        }
    }
    
    function setupFullscreenFix() {
        console.log('设置 TinyMCE 全屏修复');
        
        // 立即添加修复样式
        addFullscreenFixStyles();
        
        // 监听 TinyMCE 编辑器初始化
        tinymce.on('AddEditor', function(e) {
            var editor = e.editor;
            console.log('编辑器已添加:', editor.id);
            
            // 监听全屏状态变化
            editor.on('FullscreenStateChanged', function(e) {
                console.log('全屏状态变化:', e.state);
                
                if (e.state) {
                    // 进入全屏模式
                    console.log('进入全屏模式，开始修复');
                    
                    // 立即修复
                    fixFullscreenToolbar();
                    
                    // 多次尝试修复，确保成功
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 50);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 200);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 500);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 1000);
                } else {
                    // 退出全屏模式
                    console.log('退出全屏模式');
                }
            });
        });
    }
    
    function fixFullscreenToolbar() {
        console.log('开始修复全屏工具栏');
        
        // 查找全屏容器
        var fullscreenContainer = document.querySelector('.tox-fullscreen');
        if (!fullscreenContainer) {
            console.log('未找到全屏容器，尝试查找其他可能的容器');
            // 尝试查找其他可能的全屏容器
            fullscreenContainer = document.querySelector('.tox-editor-container[style*="position: fixed"]') ||
                                 document.querySelector('.tox-editor-container[style*="z-index"]') ||
                                 document.querySelector('.tox-tinymce[style*="position: fixed"]');
        }
        
        if (!fullscreenContainer) {
            console.log('仍未找到全屏容器');
            return;
        }
        
        console.log('找到全屏容器:', fullscreenContainer);
        
        // 强制设置容器样式
        fullscreenContainer.style.position = 'fixed';
        fullscreenContainer.style.top = '0';
        fullscreenContainer.style.left = '0';
        fullscreenContainer.style.width = '100%';
        fullscreenContainer.style.height = '100%';
        fullscreenContainer.style.zIndex = '10000';
        
        // 修复工具栏
        var toolbar = fullscreenContainer.querySelector('.tox-toolbar');
        if (toolbar) {
            toolbar.style.display = 'flex';
            toolbar.style.visibility = 'visible';
            toolbar.style.opacity = '1';
            toolbar.style.position = 'relative';
            toolbar.style.zIndex = '10001';
            toolbar.style.height = 'auto';
            toolbar.style.minHeight = '40px';
            console.log('工具栏已修复');
        } else {
            console.log('未找到工具栏');
        }
        
        // 修复菜单栏
        var menubar = fullscreenContainer.querySelector('.tox-menubar');
        if (menubar) {
            menubar.style.display = 'flex';
            menubar.style.visibility = 'visible';
            menubar.style.opacity = '1';
            menubar.style.position = 'relative';
            menubar.style.zIndex = '10001';
            menubar.style.height = 'auto';
            console.log('菜单栏已修复');
        } else {
            console.log('未找到菜单栏');
        }
        
        // 修复编辑器头部
        var editorHeader = fullscreenContainer.querySelector('.tox-editor-header');
        if (editorHeader) {
            editorHeader.style.display = 'block';
            editorHeader.style.visibility = 'visible';
            editorHeader.style.position = 'relative';
            editorHeader.style.top = '0';
            editorHeader.style.zIndex = '10001';
            console.log('编辑器头部已修复');
        }
        
        // 修复工具栏容器
        var toolbarOverlord = fullscreenContainer.querySelector('.tox-toolbar-overlord');
        if (toolbarOverlord) {
            toolbarOverlord.style.display = 'block';
            toolbarOverlord.style.visibility = 'visible';
            toolbarOverlord.style.height = 'auto';
            console.log('工具栏容器已修复');
        }
        
        // 确保编辑区域正确显示
        var editArea = fullscreenContainer.querySelector('.tox-edit-area');
        if (editArea) {
            editArea.style.paddingTop = '0';
            console.log('编辑区域已修复');
        }
        
        console.log('全屏工具栏修复完成');
    }
    
    // 页面加载完成后开始监听
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            addFullscreenFixStyles();
            waitForTinyMCE();
        });
    } else {
        addFullscreenFixStyles();
        waitForTinyMCE();
    }
    
    // 额外的全局修复函数
    window.fixTinyMCEFullscreen = function() {
        console.log('手动触发全屏修复');
        addFullscreenFixStyles();
        fixFullscreenToolbar();
    };
    
})();
