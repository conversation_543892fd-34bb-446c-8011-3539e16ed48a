/**
 * TinyMCE 全屏模式工具栏修复脚本
 * 解决全屏模式下工具栏和菜单栏消失的问题
 */
(function() {
    'use strict';
    
    console.log('TinyMCE 全屏修复脚本已加载');
    
    // 强制添加全屏修复样式
    function addFullscreenFixStyles() {
        const style = document.createElement('style');
        style.id = 'tinymce-fullscreen-fix-styles';
        style.textContent = `
            /* 底部工具栏样式 - 只显示格式化按钮 */
            .tox-fullscreen-toolbar-fix {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 10002 !important;
                background: #f8f8f8 !important;
                border-top: 1px solid #ddd !important;
                display: flex !important;
                padding: 3px !important;
                box-shadow: 0 -2px 4px rgba(0,0,0,0.1) !important;
                height: 60px !important;
                overflow: hidden !important;
            }

            /* 全屏容器调整 - 为底部工具栏预留空间 */
            .tox-fullscreen.tox-tinymce {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 60px !important;
                width: 100% !important;
                height: calc(100% - 60px) !important;
                z-index: 10000 !important;
            }

            /* 只隐藏原有的工具栏，保留菜单栏在顶部 */
            .tox-fullscreen.tox-tinymce .tox-toolbar {
                display: none !important;
            }

            /* 确保菜单栏显示在顶部 */
            .tox-fullscreen.tox-tinymce .tox-menubar {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* 编辑区域占据剩余空间 */
            .tox-fullscreen.tox-tinymce .tox-edit-area {
                height: 100% !important;
                width: 100% !important;
                overflow: auto !important;
            }
        `;
        
        // 移除旧样式（如果存在）
        const oldStyle = document.getElementById('tinymce-fullscreen-fix-styles');
        if (oldStyle) {
            oldStyle.remove();
        }
        
        document.head.appendChild(style);
        console.log('全屏修复样式已添加');
    }
    
    // 等待 TinyMCE 加载完成
    function waitForTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            setupFullscreenFix();
        } else {
            setTimeout(waitForTinyMCE, 100);
        }
    }
    
    function setupFullscreenFix() {
        console.log('设置 TinyMCE 全屏修复');
        
        // 立即添加修复样式
        addFullscreenFixStyles();
        
        // 监听 TinyMCE 编辑器初始化
        tinymce.on('AddEditor', function(e) {
            var editor = e.editor;
            console.log('编辑器已添加:', editor.id);
            
            // 监听全屏状态变化
            editor.on('FullscreenStateChanged', function(e) {
                console.log('全屏状态变化:', e.state);
                
                if (e.state) {
                    // 进入全屏模式
                    console.log('进入全屏模式，开始修复');
                    
                    // 立即修复
                    fixFullscreenToolbar();
                    
                    // 多次尝试修复，确保成功
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 50);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 200);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 500);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 1000);
                } else {
                    // 退出全屏模式
                    console.log('退出全屏模式');
                }
            });
        });
    }
    
    function fixFullscreenToolbar() {
        console.log('开始创建底部工具栏（只包含格式化按钮）');

        // 查找全屏容器
        var fullscreenContainer = document.querySelector('.tox-fullscreen');
        if (!fullscreenContainer) {
            console.log('未找到全屏容器');
            return;
        }

        // 移除之前创建的工具栏（如果存在）
        var existingToolbar = document.querySelector('.tox-fullscreen-toolbar-fix');
        if (existingToolbar) {
            existingToolbar.remove();
        }

        // 查找原始工具栏（格式化按钮）
        var originalToolbar = fullscreenContainer.querySelector('.tox-toolbar');

        if (!originalToolbar) {
            console.log('未找到原始工具栏');
            return;
        }

        // 创建底部工具栏容器
        var bottomToolbar = document.createElement('div');
        bottomToolbar.className = 'tox-fullscreen-toolbar-fix';

        // 只复制工具栏内容（格式化按钮），不复制菜单栏
        var toolbarClone = originalToolbar.cloneNode(true);
        toolbarClone.style.display = 'flex';
        toolbarClone.style.width = '100%';
        toolbarClone.style.flexWrap = 'wrap';
        toolbarClone.style.justifyContent = 'flex-start';
        toolbarClone.style.padding = '5px';
        bottomToolbar.appendChild(toolbarClone);
        console.log('复制了工具栏到底部');

        // 将工具栏添加到页面底部
        document.body.appendChild(bottomToolbar);
        console.log('底部工具栏已创建');

        // 调整全屏容器 - 为底部工具栏预留空间
        fullscreenContainer.style.position = 'fixed';
        fullscreenContainer.style.top = '0';
        fullscreenContainer.style.left = '0';
        fullscreenContainer.style.right = '0';
        fullscreenContainer.style.bottom = '60px';  // 为工具栏预留空间
        fullscreenContainer.style.width = '100%';
        fullscreenContainer.style.height = 'calc(100% - 60px)';
        fullscreenContainer.style.zIndex = '10000';

        // 隐藏原始工具栏（只隐藏格式化按钮工具栏）
        if (originalToolbar) {
            originalToolbar.style.display = 'none';
        }

        // 保持菜单栏显示在顶部
        var menubar = fullscreenContainer.querySelector('.tox-menubar');
        if (menubar) {
            menubar.style.display = 'flex';
            menubar.style.visibility = 'visible';
            menubar.style.opacity = '1';
            console.log('菜单栏保持在顶部显示');
        }

        // 确保编辑区域正确显示
        var editArea = fullscreenContainer.querySelector('.tox-edit-area');
        if (editArea) {
            editArea.style.height = '100%';
            editArea.style.width = '100%';
            editArea.style.overflow = 'auto';
            console.log('编辑区域已调整');
        }

        console.log('全屏工具栏修复完成 - 格式化工具栏现在在底部');
    }
    
    // 页面加载完成后开始监听
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            addFullscreenFixStyles();
            waitForTinyMCE();
        });
    } else {
        addFullscreenFixStyles();
        waitForTinyMCE();
    }
    
    // 额外的全局修复函数
    window.fixTinyMCEFullscreen = function() {
        console.log('手动触发全屏修复');
        addFullscreenFixStyles();
        fixFullscreenToolbar();
    };
    
})();
