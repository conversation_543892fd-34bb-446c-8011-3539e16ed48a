/**
 * TinyMCE 全屏模式工具栏修复脚本
 * 解决全屏模式下工具栏和菜单栏消失的问题
 */
(function() {
    'use strict';
    
    console.log('TinyMCE 全屏修复脚本已加载');

    // 存储原始样式，用于恢复
    var originalStyles = new Map();

    // 保存原始样式
    function saveOriginalStyles() {
        console.log('保存原始样式');
        originalStyles.clear();

        var editorContainer = document.querySelector('.tox-tinymce');
        if (editorContainer) {
            originalStyles.set('container', {
                position: editorContainer.style.position,
                top: editorContainer.style.top,
                left: editorContainer.style.left,
                right: editorContainer.style.right,
                bottom: editorContainer.style.bottom,
                width: editorContainer.style.width,
                height: editorContainer.style.height,
                zIndex: editorContainer.style.zIndex
            });
        }
    }
    
    // 强制添加全屏修复样式
    function addFullscreenFixStyles() {
        const style = document.createElement('style');
        style.id = 'tinymce-fullscreen-fix-styles';
        style.textContent = `
            /* 底部工具栏样式 - 只显示格式化按钮 */
            .tox-fullscreen-toolbar-fix {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 10002 !important;
                background: #f8f8f8 !important;
                border-top: 1px solid #ddd !important;
                display: flex !important;
                padding: 3px !important;
                box-shadow: 0 -2px 4px rgba(0,0,0,0.1) !important;
                height: 60px !important;
                overflow: hidden !important;
            }

            /* 全屏容器调整 - 为底部工具栏预留空间 */
            .tox-fullscreen.tox-tinymce {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 60px !important;
                width: 100% !important;
                height: calc(100% - 60px) !important;
                z-index: 10000 !important;
            }

            /* 只隐藏原有的工具栏，保留菜单栏在顶部 */
            .tox-fullscreen.tox-tinymce .tox-toolbar {
                display: none !important;
            }

            /* 确保菜单栏显示在顶部 */
            .tox-fullscreen.tox-tinymce .tox-menubar {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* 编辑区域占据剩余空间 */
            .tox-fullscreen.tox-tinymce .tox-edit-area {
                height: 100% !important;
                width: 100% !important;
                overflow: auto !important;
            }
        `;
        
        // 移除旧样式（如果存在）
        const oldStyle = document.getElementById('tinymce-fullscreen-fix-styles');
        if (oldStyle) {
            oldStyle.remove();
        }
        
        document.head.appendChild(style);
        console.log('全屏修复样式已添加');
    }
    
    // 等待 TinyMCE 加载完成
    function waitForTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            setupFullscreenFix();
        } else {
            setTimeout(waitForTinyMCE, 100);
        }
    }
    
    function setupFullscreenFix() {
        console.log('设置 TinyMCE 全屏修复');
        
        // 立即添加修复样式
        addFullscreenFixStyles();
        
        // 监听 TinyMCE 编辑器初始化
        tinymce.on('AddEditor', function(e) {
            var editor = e.editor;
            console.log('编辑器已添加:', editor.id);
            
            // 监听全屏状态变化
            editor.on('FullscreenStateChanged', function(e) {
                console.log('全屏状态变化:', e.state);
                
                if (e.state) {
                    // 进入全屏模式
                    console.log('进入全屏模式，开始修复');

                    // 保存原始样式
                    saveOriginalStyles();

                    // 立即修复
                    fixFullscreenToolbar();
                    
                    // 多次尝试修复，确保成功
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 50);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 200);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 500);
                    
                    setTimeout(function() {
                        fixFullscreenToolbar();
                    }, 1000);
                } else {
                    // 退出全屏模式
                    console.log('退出全屏模式，开始清理');
                    cleanupFullscreenToolbar();
                }
            });
        });
    }
    
    function fixFullscreenToolbar() {
        console.log('开始创建底部工具栏（只包含格式化按钮）');

        // 查找全屏容器
        var fullscreenContainer = document.querySelector('.tox-fullscreen');
        if (!fullscreenContainer) {
            console.log('未找到全屏容器');
            return;
        }

        // 移除之前创建的工具栏（如果存在）
        var existingToolbar = document.querySelector('.tox-fullscreen-toolbar-fix');
        if (existingToolbar) {
            existingToolbar.remove();
        }

        // 查找完整的工具栏容器（包含所有格式化按钮）
        var originalToolbar = fullscreenContainer.querySelector('.tox-toolbar-overlord') ||
                             fullscreenContainer.querySelector('.tox-toolbar') ||
                             fullscreenContainer.querySelector('.tox-editor-header') ||
                             document.querySelector('.tox-toolbar-overlord') ||
                             document.querySelector('.tox-toolbar');

        console.log('查找工具栏结果:', originalToolbar);

        // 如果找到的是工具栏组，尝试找到其父容器
        if (originalToolbar && originalToolbar.classList.contains('tox-toolbar__group')) {
            var parentToolbar = originalToolbar.closest('.tox-toolbar-overlord') ||
                               originalToolbar.closest('.tox-toolbar') ||
                               originalToolbar.closest('.tox-editor-header');
            if (parentToolbar) {
                originalToolbar = parentToolbar;
                console.log('找到父工具栏容器:', originalToolbar);
            }
        }

        if (!originalToolbar) {
            console.log('未找到工具栏容器，尝试查找编辑器头部');
            var editorHeader = document.querySelector('.tox-editor-header');
            if (editorHeader) {
                originalToolbar = editorHeader;
                console.log('使用编辑器头部作为工具栏:', originalToolbar);
            } else {
                console.log('完全没有找到工具栏');
                return;
            }
        }

        // 创建底部工具栏容器
        var bottomToolbar = document.createElement('div');
        bottomToolbar.className = 'tox-fullscreen-toolbar-fix';

        // 只复制工具栏内容（格式化按钮），不复制菜单栏
        var toolbarClone = originalToolbar.cloneNode(true);

        // 强制设置克隆工具栏的样式，确保可见
        toolbarClone.style.display = 'flex';
        toolbarClone.style.width = '100%';
        toolbarClone.style.flexWrap = 'wrap';
        toolbarClone.style.justifyContent = 'flex-start';
        toolbarClone.style.padding = '5px';
        toolbarClone.style.visibility = 'visible';
        toolbarClone.style.opacity = '1';
        toolbarClone.style.position = 'relative';
        toolbarClone.style.zIndex = '1';

        // 确保所有子元素也是可见的
        var allChildren = toolbarClone.querySelectorAll('*');
        allChildren.forEach(function(child) {
            if (child.style.display === 'none') {
                child.style.display = '';
            }
            child.style.visibility = 'visible';
            child.style.opacity = '1';
        });

        bottomToolbar.appendChild(toolbarClone);
        console.log('复制了工具栏到底部');
        console.log('克隆的工具栏:', toolbarClone);
        console.log('克隆工具栏的子元素数量:', toolbarClone.children.length);

        // 将工具栏添加到页面底部
        document.body.appendChild(bottomToolbar);
        console.log('底部工具栏已创建');

        // 强制设置底部工具栏的样式，确保可见
        bottomToolbar.style.position = 'fixed';
        bottomToolbar.style.bottom = '0px';
        bottomToolbar.style.left = '0px';
        bottomToolbar.style.right = '0px';
        bottomToolbar.style.zIndex = '10002';
        bottomToolbar.style.background = '#f8f8f8';
        bottomToolbar.style.borderTop = '1px solid #ddd';
        bottomToolbar.style.display = 'flex';
        bottomToolbar.style.padding = '8px';
        bottomToolbar.style.boxShadow = '0 -2px 4px rgba(0,0,0,0.1)';
        bottomToolbar.style.height = '60px';
        bottomToolbar.style.overflow = 'visible';
        bottomToolbar.style.visibility = 'visible';
        bottomToolbar.style.opacity = '1';

        console.log('底部工具栏样式已强制设置');
        console.log('底部工具栏元素:', bottomToolbar);
        console.log('底部工具栏是否在页面中:', document.body.contains(bottomToolbar));

        // 添加调试信息
        setTimeout(function() {
            var debugToolbar = document.querySelector('.tox-fullscreen-toolbar-fix');
            if (debugToolbar) {
                console.log('调试：找到底部工具栏');
                console.log('调试：工具栏位置:', debugToolbar.getBoundingClientRect());
                console.log('调试：工具栏样式:', window.getComputedStyle(debugToolbar));
                console.log('调试：工具栏内容:', debugToolbar.innerHTML.substring(0, 200));
            } else {
                console.log('调试：未找到底部工具栏');
            }
        }, 100);

        // 调整全屏容器 - 为底部工具栏预留空间
        fullscreenContainer.style.position = 'fixed';
        fullscreenContainer.style.top = '0';
        fullscreenContainer.style.left = '0';
        fullscreenContainer.style.right = '0';
        fullscreenContainer.style.bottom = '60px';  // 为工具栏预留空间
        fullscreenContainer.style.width = '100%';
        fullscreenContainer.style.height = 'calc(100% - 60px)';
        fullscreenContainer.style.zIndex = '10000';

        // 隐藏原始工具栏容器
        if (originalToolbar) {
            originalToolbar.style.display = 'none';
            console.log('已隐藏原始工具栏容器');
        }

        // 确保隐藏所有相关的工具栏元素
        var toolbarElements = fullscreenContainer.querySelectorAll('.tox-toolbar-overlord, .tox-toolbar, .tox-editor-header');
        toolbarElements.forEach(function(element) {
            element.style.display = 'none';
            console.log('隐藏了工具栏元素:', element.className);
        });

        // 保持菜单栏显示在顶部
        var menubar = fullscreenContainer.querySelector('.tox-menubar');
        if (menubar) {
            menubar.style.display = 'flex';
            menubar.style.visibility = 'visible';
            menubar.style.opacity = '1';
            console.log('菜单栏保持在顶部显示');
        }

        // 确保编辑区域正确显示
        var editArea = fullscreenContainer.querySelector('.tox-edit-area');
        if (editArea) {
            editArea.style.height = '100%';
            editArea.style.width = '100%';
            editArea.style.overflow = 'auto';
            console.log('编辑区域已调整');
        }

        console.log('全屏工具栏修复完成 - 格式化工具栏现在在底部');
    }

    // 清理函数 - 退出全屏时完全恢复原始状态
    function cleanupFullscreenToolbar() {
        console.log('清理全屏工具栏并完全恢复原始状态');

        // 移除底部工具栏
        var bottomToolbar = document.querySelector('.tox-fullscreen-toolbar-fix');
        if (bottomToolbar) {
            bottomToolbar.remove();
            console.log('底部工具栏已移除');
        }

        // 查找编辑器容器
        var editorContainer = document.querySelector('.tox-tinymce');
        if (editorContainer) {
            console.log('找到编辑器容器，开始恢复原始状态');

            // 恢复容器原始样式
            editorContainer.style.position = '';
            editorContainer.style.top = '';
            editorContainer.style.left = '';
            editorContainer.style.right = '';
            editorContainer.style.bottom = '';
            editorContainer.style.width = '';
            editorContainer.style.height = '';
            editorContainer.style.zIndex = '';
            console.log('容器样式已恢复');

            // 恢复所有工具栏元素的原始显示状态
            var allToolbarElements = document.querySelectorAll('.tox-toolbar-overlord, .tox-toolbar, .tox-editor-header, [role="toolbar"]');
            allToolbarElements.forEach(function(element) {
                element.style.display = '';
                element.style.visibility = '';
                element.style.opacity = '';
                element.style.position = '';
                element.style.zIndex = '';
                element.style.height = '';
                element.style.minHeight = '';
                console.log('工具栏元素已恢复原始状态:', element.className);
            });

            var menubar = editorContainer.querySelector('.tox-menubar');
            if (menubar) {
                menubar.style.display = '';
                menubar.style.visibility = '';
                menubar.style.opacity = '';
                menubar.style.position = '';
                menubar.style.zIndex = '';
                menubar.style.height = '';
                console.log('菜单栏已恢复原始状态');
            }

            var editorHeader = editorContainer.querySelector('.tox-editor-header');
            if (editorHeader) {
                editorHeader.style.display = '';
                editorHeader.style.visibility = '';
                editorHeader.style.position = '';
                editorHeader.style.top = '';
                editorHeader.style.zIndex = '';
                console.log('编辑器头部已恢复原始状态');
            }

            var editArea = editorContainer.querySelector('.tox-edit-area');
            if (editArea) {
                editArea.style.height = '';
                editArea.style.width = '';
                editArea.style.overflow = '';
                editArea.style.paddingTop = '';
                console.log('编辑区域已恢复原始状态');
            }

            console.log('编辑器已完全恢复到原始状态');
        }

        console.log('全屏工具栏清理完成');
    }

    // 页面加载完成后开始监听
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            addFullscreenFixStyles();
            waitForTinyMCE();
        });
    } else {
        addFullscreenFixStyles();
        waitForTinyMCE();
    }
    
    // 额外的全局修复函数
    window.fixTinyMCEFullscreen = function() {
        console.log('手动触发全屏修复');
        addFullscreenFixStyles();
        fixFullscreenToolbar();
    };
    
})();
