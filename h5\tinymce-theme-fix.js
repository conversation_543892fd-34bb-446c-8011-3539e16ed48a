/**
 * TinyMCE 主题触摸事件修复脚本
 * 专门解决 theme.min.js 中的非被动触摸事件警告
 */

(function() {
    'use strict';
    
    // 检测是否支持被动事件监听器
    let supportsPassive = false;
    try {
        const opts = Object.defineProperty({}, 'passive', {
            get: function() {
                supportsPassive = true;
            }
        });
        window.addEventListener('testPassive', null, opts);
        window.removeEventListener('testPassive', null, opts);
    } catch (e) {
        // 不支持被动事件监听器
    }
    
    if (!supportsPassive) {
        console.log('浏览器不支持被动事件监听器，跳过触摸事件优化');
        return;
    }
    
    // 保存原始的 addEventListener
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    // 修补 addEventListener 方法
    EventTarget.prototype.addEventListener = function(type, listener, options) {
        // 只处理触摸事件
        if (type === 'touchstart' || type === 'touchmove') {
            // 检查调用栈，看是否来自 TinyMCE 相关代码
            const stack = new Error().stack || '';
            const isTinyMCERelated = stack.includes('theme.min.js') || 
                                   stack.includes('tinymce.min.js') ||
                                   (this.className && this.className.includes('tox-')) ||
                                   (this.closest && this.closest('.tox-editor-container'));
            
            if (isTinyMCERelated) {
                // 强制使用被动监听器
                if (typeof options === 'boolean') {
                    options = { capture: options, passive: true };
                } else if (typeof options === 'object' && options !== null) {
                    options = { ...options, passive: true };
                } else {
                    options = { passive: true };
                }
                
                // 静默处理，避免在控制台显示修复信息
                // console.log(`🔧 已将 ${type} 事件转换为被动监听器`);
            }
        }
        
        return originalAddEventListener.call(this, type, listener, options);
    };
    
    // 为已存在的 TinyMCE 元素添加被动事件监听器
    function addPassiveListenersToExistingElements() {
        // 查找所有 TinyMCE 相关元素
        const tinyMCEElements = document.querySelectorAll('.tox-editor-container, .tox-toolbar, .tox-edit-area, [class*="tox-"]');
        
        tinyMCEElements.forEach(element => {
            // 为每个元素添加被动触摸事件监听器
            element.addEventListener('touchstart', function() {
                // 被动监听器，提升滚动性能
            }, { passive: true });
            
            element.addEventListener('touchmove', function() {
                // 被动监听器，提升滚动性能
            }, { passive: true });
        });
    }
    
    // 监听 TinyMCE 编辑器初始化
    function monitorTinyMCEInit() {
        // 如果 TinyMCE 已经存在
        if (typeof tinymce !== 'undefined') {
            setupTinyMCEEventOptimization();
        } else {
            // 等待 TinyMCE 加载
            const checkInterval = setInterval(() => {
                if (typeof tinymce !== 'undefined') {
                    clearInterval(checkInterval);
                    setupTinyMCEEventOptimization();
                }
            }, 100);
            
            // 10秒后停止检查
            setTimeout(() => clearInterval(checkInterval), 10000);
        }
    }
    
    // 设置 TinyMCE 事件优化
    function setupTinyMCEEventOptimization() {
        try {
            // 监听编辑器添加事件
            tinymce.on('AddEditor', function(e) {
                const editor = e.editor;
                
                editor.on('init', function() {
                    setTimeout(() => {
                        const container = editor.getContainer();
                        if (container) {
                            // 为新创建的编辑器添加被动事件监听器
                            addPassiveListenersToContainer(container);
                        }
                    }, 100);
                });
            });
            
            // 如果已经有活动编辑器，立即处理
            if (tinymce.activeEditor) {
                const container = tinymce.activeEditor.getContainer();
                if (container) {
                    addPassiveListenersToContainer(container);
                }
            }
        } catch (error) {
            console.warn('设置 TinyMCE 事件优化时出错:', error);
        }
    }
    
    // 为容器添加被动事件监听器
    function addPassiveListenersToContainer(container) {
        // 为容器本身添加
        container.addEventListener('touchstart', function() {}, { passive: true });
        container.addEventListener('touchmove', function() {}, { passive: true });
        
        // 为子元素添加
        const subElements = container.querySelectorAll('.tox-toolbar, .tox-edit-area, .tox-menubar, [class*="tox-"]');
        subElements.forEach(element => {
            element.addEventListener('touchstart', function() {}, { passive: true });
            element.addEventListener('touchmove', function() {}, { passive: true });
        });
    }
    
    // 立即开始监听
    monitorTinyMCEInit();
    
    // DOM 加载完成后处理已存在的元素
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addPassiveListenersToExistingElements);
    } else {
        addPassiveListenersToExistingElements();
    }
    
    // 使用 MutationObserver 监听新添加的 TinyMCE 元素
    function setupMutationObserver() {
        if (typeof MutationObserver !== 'undefined' && document.body) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // 元素节点
                            // 检查是否是 TinyMCE 相关元素
                            if (node.className && node.className.includes('tox-')) {
                                addPassiveListenersToContainer(node);
                            }

                            // 检查子元素
                            const tinyMCEChildren = node.querySelectorAll && node.querySelectorAll('[class*="tox-"]');
                            if (tinyMCEChildren) {
                                tinyMCEChildren.forEach(addPassiveListenersToContainer);
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // 等待 document.body 可用后设置 MutationObserver
    if (document.body) {
        setupMutationObserver();
    } else {
        document.addEventListener('DOMContentLoaded', setupMutationObserver);
    }
    
    console.log('🚀 TinyMCE 主题触摸事件优化已启用');
    
})();
